/* Portfolio Projects Analytics Styles - Enhanced Portfolio Theme */
.portfolio-analytics-container {
  max-width: 1400px;
  margin: 20px auto;
  padding: 50px 30px;
  background:
    linear-gradient(135deg, rgba(75,0,130,0.2) 0%, rgba(255,45,85,0.15) 100%),
    radial-gradient(circle at 20% 20%, rgba(75,0,130,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255,45,85,0.1) 0%, transparent 50%);
  border-radius: 35px;
  box-shadow:
    0 25px 50px rgba(75,0,130,0.25),
    0 15px 35px rgba(255,45,85,0.15),
    0 5px 15px rgba(0,0,0,0.3),
    inset 0 1px 0 rgba(255,255,255,0.1);
  font-family: 'Montserrat', sans-serif;
  color: #fff;
  position: relative;
  backdrop-filter: blur(20px);
  border: 2px solid rgba(255,255,255,0.1);
  min-height: 90vh;
  animation: dashboardFadeIn 0.8s ease-out;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

@keyframes dashboardFadeIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Add floating particles effect */
.portfolio-analytics-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255,45,85,0.1) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(75,0,130,0.1) 1px, transparent 1px);
  background-size: 50px 50px, 30px 30px;
  animation: floatingParticles 20s linear infinite;
  pointer-events: none;
  border-radius: 35px;
}

@keyframes floatingParticles {
  0% { transform: translateY(0px) rotate(0deg); }
  100% { transform: translateY(-20px) rotate(360deg); }
}

.analytics-header {
  text-align: center;
  margin-bottom: 50px;
  position: relative;
  z-index: 2;
  padding-top: 60px; /* Add space for the back button */
}

.analytics-header h1 {
  font-size: 3rem;
  font-weight: 900;
  letter-spacing: 3px;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #4B0082 0%, #FF2D55 50%, #4B0082 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  text-transform: uppercase;
  text-align: center;
  position: relative;
  padding: 20px 0;
}

.back-button {
  position: absolute;
  left: 0;
  top: 0;
  background: rgba(255,255,255,0.1);
  border: 2px solid rgba(255,45,85,0.3);
  color: #fff;
  padding: 15px 25px;
  border-radius: 25px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(15px);
  text-transform: uppercase;
  letter-spacing: 1px;
  z-index: 10; /* Ensure it's above other elements */
}

.back-button:hover {
  background: rgba(255,45,85,0.2);
  border-color: rgba(255,45,85,0.6);
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(255,45,85,0.3);
  color: #fff;
}

.analytics-header p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  margin: 0;
}

/* Loading and Error States */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  position: relative;
  z-index: 2;
}

.spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255,255,255,0.2);
  border-top: 4px solid #FF2D55;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-spinner p {
  font-size: 1.2rem;
  font-weight: 600;
  color: #fff;
  text-align: center;
  margin: 0;
  letter-spacing: 1px;
}

.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  position: relative;
  z-index: 2;
  text-align: center;
  padding: 40px;
}

.error-message h3 {
  font-size: 2rem;
  font-weight: 700;
  color: #FF2D55;
  margin-bottom: 15px;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.error-message p {
  font-size: 1.1rem;
  color: rgba(255,255,255,0.8);
  margin-bottom: 30px;
  line-height: 1.6;
  max-width: 600px;
}

.retry-button {
  background: linear-gradient(135deg, #FF2D55 0%, #4B0082 100%);
  border: none;
  color: #fff;
  padding: 15px 30px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(255,45,85,0.3);
}

.retry-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255,45,85,0.4);
}

.analytics-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
  position: relative;
  z-index: 2;
}

.summary-card {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  padding: 25px;
  display: flex;
  align-items: center;
  gap: 20px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
}

.summary-icon {
  font-size: 2.5rem;
  color: #FFD700;
}

.summary-content h3 {
  color: white;
  margin: 0 0 5px 0;
  font-size: 1rem;
  opacity: 0.9;
}

.summary-value {
  color: white;
  font-size: 2rem;
  font-weight: bold;
  margin: 0;
}

.availability-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
  position: relative;
  z-index: 2;
}

.availability-card {
  border-radius: 15px;
  padding: 25px;
  display: flex;
  align-items: center;
  gap: 20px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.availability-card.available {
  background: rgba(34, 197, 94, 0.1);
  border-color: rgba(34, 197, 94, 0.3);
}

.availability-card.unavailable {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
}

.availability-card:hover {
  transform: translateY(-5px);
}

.availability-icon {
  font-size: 2.5rem;
}

.availability-card.available .availability-icon {
  color: #22c55e;
}

.availability-card.unavailable .availability-icon {
  color: #ef4444;
}

.availability-content h3 {
  color: white;
  margin: 0 0 5px 0;
  font-size: 1rem;
  opacity: 0.9;
}

.availability-value {
  color: white;
  font-size: 2rem;
  font-weight: bold;
  margin: 0;
}

.tab-navigation {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-bottom: 30px;
  position: relative;
  z-index: 2;
}

.tab-button {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 12px 24px;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  font-size: 14px;
}

.tab-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.tab-button.active {
  background: rgba(255, 215, 0, 0.2);
  border-color: rgba(255, 215, 0, 0.5);
  color: #FFD700;
}

.projects-analytics-list {
  position: relative;
  z-index: 2;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 25px;
}

.project-analytics-card {
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 30px;
  backdrop-filter: blur(15px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 15px 35px rgba(75,0,130,0.2),
    0 5px 15px rgba(0,0,0,0.1),
    inset 0 1px 0 rgba(255,255,255,0.1);
  position: relative;
  overflow: hidden;
}

.project-analytics-card.available {
  border-left: 4px solid #22c55e;
}

.project-analytics-card.unavailable {
  border-left: 4px solid #ef4444;
}

.project-analytics-card:hover {
  transform: translateY(-8px);
  background: linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.08) 100%);
  box-shadow:
    0 20px 45px rgba(75,0,130,0.3),
    0 8px 25px rgba(0,0,0,0.15),
    inset 0 1px 0 rgba(255,255,255,0.15);
}

/* Project Visual Header */
.project-visual-header {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  margin-bottom: 25px;
}

.project-image-container {
  flex-shrink: 0;
  width: 120px;
  height: 80px;
  border-radius: 15px;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  border: 2px solid rgba(255,255,255,0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 25px rgba(75,0,130,0.15);
  position: relative;
}

.project-image-analytics {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 13px;
}

.for-sale-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: linear-gradient(135deg, #22c55e, #16a34a);
  color: white;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 0.7rem;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3);
}

.project-meta {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.project-link-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.85rem;
  color: rgba(255,255,255,0.7);
}

.link-icon {
  color: #FF2D55;
  font-size: 0.8rem;
}

.link-text {
  word-break: break-all;
}

.project-header {
  display: flex;
  flex-direction: column;
  gap: 15px;
  flex: 1;
}

.project-title {
  color: white;
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.project-status {
  display: flex;
  align-items: center;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: bold;
}

.status-badge.available {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.status-badge.unavailable {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.view-project-btn {
  background: rgba(255, 215, 0, 0.2);
  border: 1px solid rgba(255, 215, 0, 0.5);
  color: #FFD700;
  padding: 8px 15px;
  border-radius: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  transition: all 0.3s ease;
}

.view-project-btn:hover {
  background: rgba(255, 215, 0, 0.3);
  transform: translateY(-2px);
}

.project-actions {
  display: flex;
  justify-content: flex-start;
  margin-top: 8px;
}

.project-stats {
  margin-bottom: 25px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 15px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  background: rgba(255,255,255,0.05);
  border-radius: 12px;
  border: 1px solid rgba(255,255,255,0.1);
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: rgba(255,255,255,0.08);
  transform: translateY(-2px);
}

.stat-item.primary {
  border-left: 4px solid #FF2D55;
}

.stat-item.secondary {
  border-left: 4px solid #4B0082;
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.stat-value {
  font-size: 1.4rem;
  font-weight: 700;
  color: #fff;
  line-height: 1;
}

.stat-label {
  font-size: 0.85rem;
  color: rgba(255,255,255,0.7);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-icon {
  color: #FFD700;
  font-size: 1.2rem;
  flex-shrink: 0;
}

/* Project Details */
.project-details {
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(255,255,255,0.03);
  border-radius: 12px;
  border: 1px solid rgba(255,255,255,0.08);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255,255,255,0.05);
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-item strong {
  color: rgba(255,255,255,0.9);
  font-size: 0.9rem;
  min-width: 120px;
}

.detail-value {
  color: rgba(255,255,255,0.7);
  font-size: 0.9rem;
  text-align: right;
}

/* Interaction Types */
.interaction-types {
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(255,255,255,0.03);
  border-radius: 12px;
  border: 1px solid rgba(255,255,255,0.08);
}

.interaction-types h4 {
  color: white;
  margin: 0 0 10px 0;
  font-size: 1rem;
  font-weight: 600;
}

.types-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.interaction-type-badge {
  background: linear-gradient(135deg, #FF2D55, #FF4D6D);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(255, 45, 85, 0.3);
}

.stat-label {
  opacity: 0.8;
}

.stat-value {
  font-weight: bold;
  color: #FFD700;
}

.project-id {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  margin-bottom: 10px;
}

.interaction-types {
  margin-bottom: 15px;
}

.interaction-types strong {
  color: white;
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
}

.types-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.interaction-type-badge {
  background: rgba(255, 215, 0, 0.2);
  color: #FFD700;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
  border: 1px solid rgba(255, 215, 0, 0.3);
}

.last-interaction {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  margin-bottom: 15px;
}

.recent-interactions {
  margin-top: 25px;
  padding: 20px;
  background: rgba(255,255,255,0.03);
  border-radius: 12px;
  border: 1px solid rgba(255,255,255,0.08);
}

.recent-interactions h4 {
  color: white;
  margin: 0 0 15px 0;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.recent-interactions h4::before {
  content: '📊';
  font-size: 1rem;
}

.interactions-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.interaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border-left: 3px solid #FF2D55;
  transition: all 0.3s ease;
}

.interaction-item:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateX(5px);
}

.interaction-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.interaction-ip {
  color: #fff;
  font-weight: 600;
  font-size: 0.9rem;
}

.interaction-type {
  color: #FF2D55;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.interaction-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
  text-align: right;
}

.interaction-time {
  color: rgba(255,255,255,0.8);
  font-size: 0.85rem;
}

.interaction-duration {
  color: rgba(255,255,255,0.6);
  font-size: 0.8rem;
}

.interactions-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.interaction-item {
  display: grid;
  grid-template-columns: 1fr 1fr 2fr;
  gap: 10px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

.interaction-ip {
  color: #FFD700;
  font-weight: bold;
}

.interaction-type {
  color: #22c55e;
  font-weight: bold;
}

.loading-spinner, .error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  color: white;
  text-align: center;
  position: relative;
  z-index: 2;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.retry-button {
  background: rgba(255, 215, 0, 0.2);
  border: 1px solid rgba(255, 215, 0, 0.5);
  color: #FFD700;
  padding: 10px 20px;
  border-radius: 25px;
  cursor: pointer;
  margin-top: 15px;
  transition: all 0.3s ease;
}

.retry-button:hover {
  background: rgba(255, 215, 0, 0.3);
  transform: translateY(-2px);
}

.no-data {
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  padding: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  backdrop-filter: blur(10px);
}

.no-data p {
  margin: 10px 0;
  font-size: 1.1rem;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .portfolio-analytics-container {
    padding: 15px;
  }
  
  .analytics-header h1 {
    font-size: 2rem;
  }
  
  .back-button {
    position: relative;
    margin-bottom: 20px;
  }
  
  .analytics-summary, .availability-summary {
    grid-template-columns: 1fr;
  }
  
  .tab-navigation {
    flex-direction: column;
    align-items: center;
  }
  
  .projects-grid {
    grid-template-columns: 1fr;
  }

  .project-visual-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 15px;
  }

  .project-image-container {
    width: 100px;
    height: 70px;
  }

  .project-header {
    align-items: center;
    text-align: center;
  }

  .project-title {
    text-align: center;
  }

  .project-actions {
    justify-content: center;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .interaction-item {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }

  .interaction-meta {
    text-align: center;
  }

  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .detail-value {
    text-align: left;
  }

  .types-list {
    justify-content: center;
  }
}
